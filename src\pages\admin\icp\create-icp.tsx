import React, { useRef, useState } from 'react';
import useExtrasStore from '@/store/extrasStore';
import useICPStore from '@/store/icpStore';
import { useEffect } from 'react';
import { Input } from '@/components/ui/input';

import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { X, Check, ChevronDown, CircleCheck, XIcon, Trash2 } from 'lucide-react';
import { CountrySelect, StateSelect } from 'react-country-state-city';
import 'react-country-state-city/dist/react-country-state-city.css';
import { toast } from 'sonner';
import useAuthStore from '@/store/authStore';
import GoBack from '@/components/GoBack';

// Custom Combo Box Component (enhanced to support multi-select, keeps existing styles)
const CustomComboBox = React.memo(({
  label,
  value,
  onValueChange,
  placeholder,
  options,
  required = false,
  isMulti = false,
  onSearch,
}: {
  label: string;
  value: string | string[];
  onValueChange: (value: string | string[]) => void;
  placeholder: string;
  options: { id: number; name: string }[];
  required?: boolean;
  isMulti?: boolean;
  onSearch?: (term: string) => void;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [inputValue, setInputValue] = useState(typeof value === 'string' ? value : '');
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const selectedValues = Array.isArray(value) ? value : [];

  const filteredOptions = options
    .filter(option => option.name.toLowerCase().includes(searchTerm.toLowerCase()))
    .filter(option => !isMulti || !selectedValues.some(v => v.toLowerCase() === option.name.toLowerCase()));

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value;
    if (!isMulti) {
      setInputValue(term);
      onValueChange(term);
    }
    setSearchTerm(term);
    setIsOpen(true);
    setSelectedIndex(-1);
    onSearch?.(term);
  };

  const addValue = (name: string) => {
    if (!isMulti) {
      setInputValue(name);
      onValueChange(name);
    } else {
      const exists = selectedValues.some(v => v.toLowerCase() === name.toLowerCase());
      if (!exists) onValueChange([...selectedValues, name]);
    }
    setSearchTerm('');
    setIsOpen(false);
    setSelectedIndex(-1);
    inputRef.current?.blur();
  };

  const removeValue = (name: string) => {
    if (isMulti) {
      onValueChange(selectedValues.filter(v => v.toLowerCase() !== name.toLowerCase()));
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (isOpen) {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedIndex(prev => (prev < filteredOptions.length - 1 ? prev + 1 : prev));
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedIndex(prev => (prev > 0 ? prev - 1 : prev));
      } else if (e.key === 'Enter') {
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < filteredOptions.length) {
          addValue(filteredOptions[selectedIndex].name);
        } else if (searchTerm) {
          addValue(searchTerm);
        }
      }
    } else if (e.key === 'Enter' && searchTerm) {
      addValue(searchTerm);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (!isMulti && typeof value === 'string') setInputValue(value);
  }, [value, isMulti]);

  useEffect(() => {
    if (selectedIndex >= 0 && dropdownRef.current) {
      const selectedOption = dropdownRef.current.querySelectorAll('.option')[selectedIndex] as HTMLElement;
      selectedOption?.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
  }, [selectedIndex]);

  return (
    <div className="flex gap-2 flex-col w-full" ref={dropdownRef}>
      <Label className="font-semibold">
        {label} {required && <span className="text-brand-secondary">*</span>}
      </Label>
      <div className="relative">

        <div className="relative">
          <Input
            ref={inputRef}
            type="text"
            value={isMulti ? searchTerm : inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={() => setIsOpen(true)}
            placeholder={placeholder}
            className="w-full capitalize bg-white !h-12 text-base pr-10"
          />
          <ChevronDown
            className={`absolute right-3 top-1/2 transform -translate-y-1/2 size-4 opacity-50 transition-transform cursor-pointer ${isOpen ? 'rotate-180' : ''}`}
            onClick={() => {
              setIsOpen(!isOpen);
              inputRef.current?.focus();
            }}
          />
        </div>

        {/* Selected tags for multi */}
        {isMulti && selectedValues.length > 0 && (
          <div className="mt-2 flex flex-wrap gap-2">
            {selectedValues.map((val) => (
              <Badge key={val} variant="secondary" className="flex items-center gap-1 px-2 py-1 rounded-full">
                <span className="capitalize">{val}</span>
                <button type="button" onClick={() => removeValue(val)} className="hover:text-red-600">
                  <X className="size-3 cursor-pointer" />
                </button>
              </Badge>
            ))}
          </div>
        )}

        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto">
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option, index) => (
                <div
                  key={option.id}
                  className={`px-3 py-2 cursor-pointer hover:bg-gray-50 flex items-center justify-between text-sm ${selectedIndex === index ? 'bg-gray-100' : ''} option`}
                  onClick={() => addValue(option.name)}
                >
                  <span className="capitalize">{option.name}</span>
                  {!isMulti && typeof value === 'string' && value === option.name && (
                    <Check className="size-4 text-brand-secondary" />
                  )}
                </div>
              ))
            ) : searchTerm ? (
              <div
                className="px-3 py-2 cursor-pointer hover:bg-gray-50 text-sm font-medium"
                onClick={() => addValue(searchTerm)}
              >
                {searchTerm}
              </div>
            ) : (
              <div className="px-3 py-2 text-gray-500 text-sm">No options found</div>
            )}
          </div>
        )}
      </div>
    </div>
  );
});

const CreateICP: React.FC = () => {
  const id = useAuthStore(state => state?.user?.id);
  const { designations, getCompanies, getDesignations } = useExtrasStore(state => state);

  const [formData, setFormData] = useState({
    sheet_name: '',
    employee_size: '',
    designation: [] as string[],
    company_name: [] as string[],
    state_name: '',
    country_name: '',
  });
  const [countryId, setCountryId] = useState<string | number | null>(null);

  const [rowPriorities, setRowPriorities] = useState<Record<string, 'P1' | 'P2' | 'P3' | 'P4'>>({});
  const [employeeSizes, setEmployeeSizes] = useState<string[]>([]);
  const [industries, setIndustries] = useState<string[]>([]);
  const [consolidatedCompanies, setConsolidatedCompanies] = useState<string[]>([]);

  const [employeeSizeSearch, setEmployeeSizeSearch] = useState('');
  const [industrySearch, setIndustrySearch] = useState('');

  // Keep formData.employee_size as a comma-separated string from tags
  useEffect(() => {
    setFormData(prev => ({ ...prev, employee_size: employeeSizes.join(', ') }));
  }, [employeeSizes]);

  // Sync consolidated companies into formData.company_name for table and payload
  useEffect(() => {
    setFormData(prev => ({ ...prev, company_name: consolidatedCompanies }));
  }, [consolidatedCompanies]);

  const rebuildConsolidatedCompanies = async (
    sizes: string[],
    inds: string[],
    sizeTerm?: string,
    indTerm?: string
  ) => {
    const merged: string[] = [];
    // Fetch by employee sizes (tags)
    for (const s of sizes) {
      try {
        await getCompanies('', undefined, s);
        const latest = useExtrasStore.getState().companies;
        merged.push(...latest.map(c => c.company));
      } catch (e) {
        console.warn('Failed fetching by employee size', s, e);
      }
    }
    // Fetch by current size term (live typing)
    if (sizeTerm && sizeTerm.trim()) {
      try {
        await getCompanies('', undefined, sizeTerm.trim());
        const latest = useExtrasStore.getState().companies;
        merged.push(...latest.map(c => c.company));
      } catch (e) {
        console.warn('Failed fetching by size term', sizeTerm, e);
      }
    }
    // Fetch by industries (tags)
    for (const ind of inds) {
      try {
        await getCompanies('', ind, undefined);
        const latest = useExtrasStore.getState().companies;
        merged.push(...latest.map(c => c.company));
      } catch (e) {
        console.warn('Failed fetching by industry', ind, e);
      }
    }
    // Fetch by current industry term (live typing)
    if (indTerm && indTerm.trim()) {
      try {
        await getCompanies('', indTerm.trim(), undefined);
        const latest = useExtrasStore.getState().companies;
        merged.push(...latest.map(c => c.company));
      } catch (e) {
        console.warn('Failed fetching by industry term', indTerm, e);
      }
    }
    const unique = Array.from(new Set(merged));
    setConsolidatedCompanies(unique);
  };

  const removeCompanyRow = (company: string) => {
    setConsolidatedCompanies(prev => prev.filter(c => c !== company));
    setRowPriorities(prev => {
      const { [company]: _removed, ...rest } = prev as Record<string, 'P1' | 'P2' | 'P3' | 'P4'>;
      return rest;
    });
  };

  useEffect(() => {
    setRowPriorities(prev => {
      const next: Record<string, 'P1' | 'P2' | 'P3' | 'P4'> = {};
      formData.company_name.forEach(name => {
        next[name] = prev[name] ?? 'P4';
      });
      return next;
    });
  }, [formData.company_name]);

  useEffect(() => {
    getDesignations('');
  }, []);

  // Debounced live search as user types in size/industry
  useEffect(() => {
    const t = setTimeout(() => {
      rebuildConsolidatedCompanies(employeeSizes, industries, employeeSizeSearch, industrySearch);
    }, 300);
    return () => clearTimeout(t);
  }, [employeeSizeSearch, industrySearch]);

  // Immediate rebuild when tags change
  useEffect(() => {
    rebuildConsolidatedCompanies(employeeSizes, industries, employeeSizeSearch, industrySearch);
  }, [employeeSizes, industries]);

  const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    console.log(name, value);
  };

  const handleCreateSheet = async () => {
    // Validation: all fields mandatory
    const isValid = Boolean(
      formData.sheet_name.trim() &&
      formData.employee_size &&
      formData.designation.length > 0 &&
      formData.company_name.length > 0 &&
      formData.country_name.trim()
    );

    if (!isValid) {
      console.warn('Please fill all required fields before proceeding.');
      return;
    }

    const payload = {
      sheet_name: formData.sheet_name.trim(),
      employee_size: formData.employee_size.trim(),
      designation: formData.designation.map(d => d.trim()).filter(Boolean),
      company_name: formData.company_name.map(c => c.trim()).filter(Boolean),
      state_name: formData.state_name.trim(),
      country_name: formData.country_name.trim(),
      priority: formData.company_name.map(company => rowPriorities[company] ?? 'P4'),
      user_id: id
    };

    try {
      const { success, message } = await useICPStore.getState().createICP(payload);
      console.log(success, message);
      if (success) {
        toast.success(message || "ICP added successfully", {
          className: "!bg-green-800 !text-white !font-sans !font-regular tracking-wider flex items-center gap-2",
          icon: <CircleCheck className='size-5' />
        });
      } else {
        toast.error(message || "Error creating ICP", {
          className: "!bg-red-800 !text-white !font-sans !font-regular tracking-wider flex items-center gap-2",
          icon: <XIcon className='size-5' />
        });
      }
    } catch (error) {
      toast.error("Failed to create ICP", {
        className: "!bg-red-800 !text-white !font-sans !font-regular tracking-wider flex items-center gap-2"
      });
    }
  };

  const isFormComplete = Boolean(
    formData.sheet_name.trim() &&
    formData.employee_size &&
    formData.designation.length > 0 &&
    formData.company_name.length > 0 &&
    formData.country_name.trim()
  );

  return (
    <div className="w-full">
      <div className='flex items-center gap-3 mb-6'>
        <GoBack />
        <h1 className='text-xl font-semibold'>Create ICP</h1>
      </div>

      <Card className="p-6 space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Sheet Name */}
          <div className="flex flex-col gap-2">
            <Label className="font-semibold">Sheet Name <span className="text-brand-secondary">*</span></Label>
            <Input
              name="sheet_name"
              value={formData.sheet_name}
              onChange={handleChange}
              placeholder="Enter sheet name"
              className="!h-12 text-base"
            />
          </div>

          {/* Employee Size - plain input with tags, no dropdown */}
          <div className="flex flex-col gap-2 w-full">
            <Label className="font-semibold">Employee Size <span className="text-brand-secondary">*</span></Label>
            <Input
              type="text"
              value={employeeSizeSearch}
              onChange={(e) => setEmployeeSizeSearch(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  const val = employeeSizeSearch.trim();
                  if (val && !employeeSizes.includes(val)) {
                    setEmployeeSizes(prev => [...prev, val]);
                  }
                  setEmployeeSizeSearch('');
                }
              }}
              placeholder="Type size (e.g., 10-50) and press Enter"
              className="w-full bg-white !h-12 text-base"
            />
            {employeeSizes.length > 0 && (
              <div className="mt-2 flex flex-wrap gap-2">
                {employeeSizes.map((val) => (
                  <Badge key={val} variant="secondary" className="flex items-center gap-1 px-2 py-1 rounded-full">
                    <span className="capitalize">{val}</span>
                    <button type="button" onClick={() => setEmployeeSizes(prev => prev.filter(v => v !== val))} className="hover:text-red-600">
                      <X className="size-3 cursor-pointer" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Designations (CustomComboBox multi) */}
          <CustomComboBox
            label="Job Title"
            isMulti
            value={formData.designation}
            onValueChange={(val) => setFormData(prev => ({ ...prev, designation: Array.isArray(val) ? val : (val ? [val] : []) }))}
            placeholder="Type or select job title"
            options={designations.map((d, index) => ({ id: index + 1, name: d.designation }))}
            onSearch={(term) => getDesignations(term)}
            required
          />

          {/* Industry - plain input with tags, no dropdown */}
          <div className="flex flex-col gap-2 w-full">
            <Label className="font-semibold">Industry</Label>
            <Input
              type="text"
              value={industrySearch}
              onChange={(e) => setIndustrySearch(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  const val = industrySearch.trim();
                  if (val && !industries.includes(val)) {
                    setIndustries(prev => [...prev, val]);
                  }
                  setIndustrySearch('');
                }
              }}
              placeholder="Type industry and press Enter"
              className="w-full bg-white !h-12 text-base"
            />
            {industries.length > 0 && (
              <div className="mt-2 flex flex-wrap gap-2">
                {industries.map((val) => (
                  <Badge key={val} variant="secondary" className="flex items-center gap-1 px-2 py-1 rounded-full">
                    <span className="capitalize">{val}</span>
                    <button type="button" onClick={() => setIndustries(prev => prev.filter(v => v !== val))} className="hover:text-red-600">
                      <X className="size-3 cursor-pointer" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Country */}
          <div className="flex flex-col gap-2">
            <Label className="font-semibold">Country <span className="text-brand-secondary">*</span></Label>
            <CountrySelect
              placeHolder="Select Country"
              onChange={(val: any) => {
                setCountryId(val?.id ?? null);
                setFormData(prev => ({ ...prev, country_name: val?.name || '', state_name: '' }));
              }}
              inputClassName="!h-12 !text-base !bg-white"
              containerClassName="!w-full"
            />
          </div>

          {/* State */}
          <div className="flex flex-col gap-2">
            <Label className="font-semibold">State</Label>
            <StateSelect
              countryid={countryId as any}
              placeHolder={countryId ? 'Select State' : 'Select country first'}
              onChange={(val: any) => setFormData(prev => ({ ...prev, state_name: val?.name || '' }))}
              inputClassName="!h-12 !text-base !bg-white"
              containerClassName="!w-full"
              disabled={!countryId}
            />
          </div>
        </div>

        {formData.company_name.length > 0 && (
          <div className="pt-2">
            <div className="overflow-x-auto rounded-md border bg-white">
              <table className="min-w-full text-sm">
                <thead className="bg-gray-50 text-gray-700">
                  <tr>
                    <th className="text-left px-4 py-3 font-semibold">Company Name</th>
                    <th className="text-left px-4 py-3 font-semibold">Designations</th>
                    <th className="text-left px-4 py-3 font-semibold">Country</th>
                    <th className="text-left px-4 py-3 font-semibold">State</th>
                    <th className="text-left px-4 py-3 font-semibold">Employee Size</th>
                    <th className="text-left px-4 py-3 font-semibold">Priority</th>
                    <th className="text-left px-4 py-3 font-semibold">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {formData.company_name.map((company) => (
                    <tr key={company} className="border-t">
                      <td className="px-4 py-3 capitalize">{company}</td>
                      <td className="px-4 py-3 capitalize">{formData.designation.join(', ')}</td>
                      <td className="px-4 py-3 capitalize">{formData.country_name}</td>
                      <td className="px-4 py-3 capitalize">{formData.state_name}</td>
                      <td className="px-4 py-3">{formData.employee_size}</td>
                      <td className="px-4 py-3">
                        <Select
                          value={rowPriorities[company] ?? 'P4'}
                          onValueChange={(v) => setRowPriorities(prev => ({ ...prev, [company]: v as 'P1' | 'P2' | 'P3' | 'P4' }))}
                        >
                          <SelectTrigger className="input !h-9 text-sm w-28">
                            <SelectValue placeholder="P4" />
                          </SelectTrigger>
                          <SelectContent>
                            {['P1', 'P2', 'P3', 'P4'].map(p => (
                              <SelectItem key={p} value={p} className="cursor-pointer">{p}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </td>
                      <td className="px-4 py-3">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCompanyRow(company)}
                          className="h-8 px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                          title="Remove"
                        >
                          <Trash2 className="size-4" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        <div className="pt-4 flex justify-end">
          <Button onClick={handleCreateSheet} disabled={!isFormComplete} className="px-6 btn">Create ICP</Button>
        </div>
      </Card>
    </div>
  );

}

export default CreateICP;